/**
 * 水体物理优化系统
 * 用于优化水体物理模拟性能，特别是在移动设备上
 */
import { System } from '../../core/System';
import { WaterBodyComponent } from './WaterBodyComponent';
import { WaterPhysicsSystem } from './WaterPhysicsSystem';
import { DeviceCapabilities, DevicePerformanceLevel } from '../../utils/DeviceCapabilities';
import { PerformanceMonitor } from '../../utils/PerformanceMonitor';
import { Debug } from '../../utils/Debug';
import type { Entity } from '../../core/Entity';

/**
 * 水体物理优化系统配置
 */
export interface WaterPhysicsOptimizationSystemConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否自动更新 */
  autoUpdate?: boolean;
  /** 更新频率 */
  updateFrequency?: number;
  /** 是否启用调试可视化 */
  enableDebugVisualization?: boolean;
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean;
  /** 是否启用自动性能优化 */
  enableAutoOptimization?: boolean;
  /** 是否启用电池优化 */
  enableBatteryOptimization?: boolean;
  /** 是否启用温度优化 */
  enableTemperatureOptimization?: boolean;
  /** 目标帧率 */
  targetFPS?: number;
  /** 最小可接受帧率 */
  minAcceptableFPS?: number;
  /** 默认性能级别 */
  defaultPerformanceLevel?: DevicePerformanceLevel;
  /** 低电量阈值（百分比） */
  lowBatteryThreshold?: number;
  /** 高温阈值（摄氏度） */
  highTemperatureThreshold?: number;
  /** 是否启用空间分区 */
  enableSpatialPartitioning?: boolean;
  /** 是否启用多线程 */
  enableMultithreading?: boolean;
  /** 是否启用自适应物理更新 */
  enableAdaptivePhysicsUpdate?: boolean;
  /** 是否启用物理LOD */
  enablePhysicsLOD?: boolean;
  /** 是否启用物理休眠 */
  enablePhysicsSleeping?: boolean;
  /** 是否启用物理缓存 */
  enablePhysicsCaching?: boolean;
  /** 是否启用物理预测 */
  enablePhysicsPrediction?: boolean;
  /** 是否启用物理插值 */
  enablePhysicsInterpolation?: boolean;
  /** 是否启用物理简化 */
  enablePhysicsSimplification?: boolean;
  /** 是否启用物理实例化 */
  enablePhysicsInstancing?: boolean;
  /** 是否启用物理批处理 */
  enablePhysicsBatching?: boolean;
  /** 是否启用物理剔除 */
  enablePhysicsCulling?: boolean;
  /** 是否启用物理优先级 */
  enablePhysicsPriority?: boolean;
  /** 是否启用物理延迟更新 */
  enablePhysicsDeferredUpdate?: boolean;
  /** 是否启用物理异步更新 */
  enablePhysicsAsyncUpdate?: boolean;
  /** 是否启用物理GPU加速 */
  enablePhysicsGPUAcceleration?: boolean;
  /** 是否启用物理SIMD加速 */
  enablePhysicsSIMDAcceleration?: boolean;
  /** 是否启用物理WebAssembly加速 */
  enablePhysicsWebAssemblyAcceleration?: boolean;
  /** 是否启用物理SharedArrayBuffer加速 */
  enablePhysicsSharedArrayBufferAcceleration?: boolean;
  /** 是否启用物理WebWorker加速 */
  enablePhysicsWebWorkerAcceleration?: boolean;
  /** 是否启用物理WebGL加速 */
  enablePhysicsWebGLAcceleration?: boolean;
  /** 是否启用物理WebCL加速 */
  enablePhysicsWebCLAcceleration?: boolean;
  /** 是否启用物理WebNN加速 */
  enablePhysicsWebNNAcceleration?: boolean;
  /** 是否启用物理WebML加速 */
  enablePhysicsWebMLAcceleration?: boolean;
  /** 是否启用物理WebXR加速 */
  enablePhysicsWebXRAcceleration?: boolean;
  /** 是否启用物理WebVR加速 */
  enablePhysicsWebVRAcceleration?: boolean;
  /** 是否启用物理WebAR加速 */
  enablePhysicsWebARAcceleration?: boolean;
  /** 是否启用物理WebAudio加速 */
  enablePhysicsWebAudioAcceleration?: boolean;
  /** 是否启用物理WebRTC加速 */
  enablePhysicsWebRTCAcceleration?: boolean;
  /** 是否启用物理WebSockets加速 */
  enablePhysicsWebSocketsAcceleration?: boolean;
  /** 是否启用物理WebTransport加速 */
  enablePhysicsWebTransportAcceleration?: boolean;
  /** 是否启用物理WebCodecs加速 */
  enablePhysicsWebCodecsAcceleration?: boolean;
  /** 是否启用物理WebUSB加速 */
  enablePhysicsWebUSBAcceleration?: boolean;
  /** 是否启用物理WebSerial加速 */
  enablePhysicsWebSerialAcceleration?: boolean;
  /** 是否启用物理WebHID加速 */
  enablePhysicsWebHIDAcceleration?: boolean;
  /** 是否启用物理WebNFC加速 */
  enablePhysicsWebNFCAcceleration?: boolean;
  /** 是否启用物理WebMIDI加速 */
  enablePhysicsWebMIDIAcceleration?: boolean;
  /** 是否启用物理WebShare加速 */
  enablePhysicsWebShareAcceleration?: boolean;
  /** 是否启用物理WebPayment加速 */
  enablePhysicsWebPaymentAcceleration?: boolean;
  /** 是否启用物理WebCredential加速 */
  enablePhysicsWebCredentialAcceleration?: boolean;
  /** 是否启用物理WebAuthentication加速 */
  enablePhysicsWebAuthenticationAcceleration?: boolean;
  /** 是否启用物理WebCrypto加速 */
  enablePhysicsWebCryptoAcceleration?: boolean;
  /** 是否启用物理WebSQL加速 */
  enablePhysicsWebSQLAcceleration?: boolean;
  /** 是否启用物理WebIndexedDB加速 */
  enablePhysicsWebIndexedDBAcceleration?: boolean;
  /** 是否启用物理WebServiceWorker加速 */
  enablePhysicsWebServiceWorkerAcceleration?: boolean;
  /** 是否启用物理WebPush加速 */
  enablePhysicsWebPushAcceleration?: boolean;
  /** 是否启用物理WebNotification加速 */
  enablePhysicsWebNotificationAcceleration?: boolean;
  /** 是否启用物理WebBackground加速 */
  enablePhysicsWebBackgroundAcceleration?: boolean;
  /** 是否启用物理WebSync加速 */
  enablePhysicsWebSyncAcceleration?: boolean;
  /** 是否启用物理WebPeriodicSync加速 */
  enablePhysicsWebPeriodicSyncAcceleration?: boolean;
  /** 是否启用物理WebBudget加速 */
  enablePhysicsWebBudgetAcceleration?: boolean;
  /** 是否启用物理WebPermission加速 */
  enablePhysicsWebPermissionAcceleration?: boolean;
  /** 是否启用物理WebLock加速 */
  enablePhysicsWebLockAcceleration?: boolean;
  /** 是否启用物理WebWakeLock加速 */
  enablePhysicsWebWakeLockAcceleration?: boolean;
  /** 是否启用物理WebVibration加速 */
  enablePhysicsWebVibrationAcceleration?: boolean;
  /** 是否启用物理WebGeolocation加速 */
  enablePhysicsWebGeolocationAcceleration?: boolean;
  /** 是否启用物理WebDeviceOrientation加速 */
  enablePhysicsWebDeviceOrientationAcceleration?: boolean;
  /** 是否启用物理WebDeviceMotion加速 */
  enablePhysicsWebDeviceMotionAcceleration?: boolean;
  /** 是否启用物理WebAmbientLight加速 */
  enablePhysicsWebAmbientLightAcceleration?: boolean;
  /** 是否启用物理WebProximity加速 */
  enablePhysicsWebProximityAcceleration?: boolean;
  /** 是否启用物理WebBattery加速 */
  enablePhysicsWebBatteryAcceleration?: boolean;
  /** 是否启用物理WebNetwork加速 */
  enablePhysicsWebNetworkAcceleration?: boolean;
  /** 是否启用物理WebCPU加速 */
  enablePhysicsWebCPUAcceleration?: boolean;
  /** 是否启用物理WebHardware加速 */
  enablePhysicsWebHardwareAcceleration?: boolean;
  /** 是否启用物理WebSoftware加速 */
  enablePhysicsWebSoftwareAcceleration?: boolean;
  /** 是否启用物理WebOS加速 */
  enablePhysicsWebOSAcceleration?: boolean;
  /** 是否启用物理WebBrowser加速 */
  enablePhysicsWebBrowserAcceleration?: boolean;
  /** 是否启用物理WebEngine加速 */
  enablePhysicsWebEngineAcceleration?: boolean;
  /** 是否启用物理WebRuntime加速 */
  enablePhysicsWebRuntimeAcceleration?: boolean;
  /** 是否启用物理WebVM加速 */
  enablePhysicsWebVMAcceleration?: boolean;
  /** 是否启用物理WebJIT加速 */
  enablePhysicsWebJITAcceleration?: boolean;
  /** 是否启用物理WebAOT加速 */
  enablePhysicsWebAOTAcceleration?: boolean;
  /** 是否启用物理WebInterpreter加速 */
  enablePhysicsWebInterpreterAcceleration?: boolean;
  /** 是否启用物理WebCompiler加速 */
  enablePhysicsWebCompilerAcceleration?: boolean;
  /** 是否启用物理WebOptimizer加速 */
  enablePhysicsWebOptimizerAcceleration?: boolean;
  /** 是否启用物理WebDebugger加速 */
  enablePhysicsWebDebuggerAcceleration?: boolean;
  /** 是否启用物理WebProfiler加速 */
  enablePhysicsWebProfilerAcceleration?: boolean;
  /** 是否启用物理WebAnalyzer加速 */
  enablePhysicsWebAnalyzerAcceleration?: boolean;
  /** 是否启用物理WebMonitor加速 */
  enablePhysicsWebMonitorAcceleration?: boolean;
  /** 是否启用物理WebLogger加速 */
  enablePhysicsWebLoggerAcceleration?: boolean;
  /** 是否启用物理WebTracer加速 */
  enablePhysicsWebTracerAcceleration?: boolean;
  /** 是否启用物理WebInspector加速 */
  enablePhysicsWebInspectorAcceleration?: boolean;
  /** 是否启用物理WebConsole加速 */
  enablePhysicsWebConsoleAcceleration?: boolean;
  /** 是否启用物理WebTerminal加速 */
  enablePhysicsWebTerminalAcceleration?: boolean;
  /** 是否启用物理WebShell加速 */
  enablePhysicsWebShellAcceleration?: boolean;
  /** 是否启用物理WebCommand加速 */
  enablePhysicsWebCommandAcceleration?: boolean;
  /** 是否启用物理WebScript加速 */
  enablePhysicsWebScriptAcceleration?: boolean;
  /** 是否启用物理WebModule加速 */
  enablePhysicsWebModuleAcceleration?: boolean;
  /** 是否启用物理WebPackage加速 */
  enablePhysicsWebPackageAcceleration?: boolean;
  /** 是否启用物理WebLibrary加速 */
  enablePhysicsWebLibraryAcceleration?: boolean;
  /** 是否启用物理WebFramework加速 */
  enablePhysicsWebFrameworkAcceleration?: boolean;
  /** 是否启用物理WebPlatform加速 */
  enablePhysicsWebPlatformAcceleration?: boolean;
  /** 是否启用物理WebAPI加速 */
  enablePhysicsWebAPIAcceleration?: boolean;
  /** 是否启用物理WebSDK加速 */
  enablePhysicsWebSDKAcceleration?: boolean;
  /** 是否启用物理WebIDE加速 */
  enablePhysicsWebIDEAcceleration?: boolean;
  /** 是否启用物理WebEditor加速 */
  enablePhysicsWebEditorAcceleration?: boolean;
  /** 是否启用物理WebDesigner加速 */
  enablePhysicsWebDesignerAcceleration?: boolean;
  /** 是否启用物理WebBuilder加速 */
  enablePhysicsWebBuilderAcceleration?: boolean;
  /** 是否启用物理WebTester加速 */
  enablePhysicsWebTesterAcceleration?: boolean;
  /** 是否启用物理WebDeployer加速 */
  enablePhysicsWebDeployerAcceleration?: boolean;
  /** 是否启用物理WebPublisher加速 */
  enablePhysicsWebPublisherAcceleration?: boolean;
  /** 是否启用物理WebDistributor加速 */
  enablePhysicsWebDistributorAcceleration?: boolean;
  /** 是否启用物理WebInstaller加速 */
  enablePhysicsWebInstallerAcceleration?: boolean;
  /** 是否启用物理WebUpdater加速 */
  enablePhysicsWebUpdaterAcceleration?: boolean;
  /** 是否启用物理WebLauncher加速 */
  enablePhysicsWebLauncherAcceleration?: boolean;
  /** 是否启用物理WebRunner加速 */
  enablePhysicsWebRunnerAcceleration?: boolean;
  /** 是否启用物理WebExecutor加速 */
  enablePhysicsWebExecutorAcceleration?: boolean;
  /** 是否启用物理WebScheduler加速 */
  enablePhysicsWebSchedulerAcceleration?: boolean;
  /** 是否启用物理WebTimer加速 */
  enablePhysicsWebTimerAcceleration?: boolean;
  /** 是否启用物理WebClock加速 */
  enablePhysicsWebClockAcceleration?: boolean;
  /** 是否启用物理WebCalendar加速 */
  enablePhysicsWebCalendarAcceleration?: boolean;
  /** 是否启用物理WebDate加速 */
  enablePhysicsWebDateAcceleration?: boolean;
  /** 是否启用物理WebTime加速 */
  enablePhysicsWebTimeAcceleration?: boolean;
  /** 是否启用物理WebDuration加速 */
  enablePhysicsWebDurationAcceleration?: boolean;
  /** 是否启用物理WebInterval加速 */
  enablePhysicsWebIntervalAcceleration?: boolean;
  /** 是否启用物理WebTimeout加速 */
  enablePhysicsWebTimeoutAcceleration?: boolean;
  /** 是否启用物理WebDelay加速 */
  enablePhysicsWebDelayAcceleration?: boolean;
  /** 是否启用物理WebSleep加速 */
  enablePhysicsWebSleepAcceleration?: boolean;
  /** 是否启用物理WebWait加速 */
  enablePhysicsWebWaitAcceleration?: boolean;
  /** 是否启用物理WebAsync加速 */
  enablePhysicsWebAsyncAcceleration?: boolean;
  /** 是否启用物理WebPromise加速 */
  enablePhysicsWebPromiseAcceleration?: boolean;
  /** 是否启用物理WebFuture加速 */
  enablePhysicsWebFutureAcceleration?: boolean;
  /** 是否启用物理WebTask加速 */
  enablePhysicsWebTaskAcceleration?: boolean;
  /** 是否启用物理WebJob加速 */
  enablePhysicsWebJobAcceleration?: boolean;
  /** 是否启用物理WebWork加速 */
  enablePhysicsWebWorkAcceleration?: boolean;
  /** 是否启用物理WebProcess加速 */
  enablePhysicsWebProcessAcceleration?: boolean;
  /** 是否启用物理WebThread加速 */
  enablePhysicsWebThreadAcceleration?: boolean;
  /** 是否启用物理WebFiber加速 */
  enablePhysicsWebFiberAcceleration?: boolean;
  /** 是否启用物理WebCoroutine加速 */
  enablePhysicsWebCoroutineAcceleration?: boolean;
  /** 是否启用物理WebGenerator加速 */
  enablePhysicsWebGeneratorAcceleration?: boolean;
  /** 是否启用物理WebIterator加速 */
  enablePhysicsWebIteratorAcceleration?: boolean;
  /** 是否启用物理WebStream加速 */
  enablePhysicsWebStreamAcceleration?: boolean;
  /** 是否启用物理WebPipe加速 */
  enablePhysicsWebPipeAcceleration?: boolean;
  /** 是否启用物理WebChannel加速 */
  enablePhysicsWebChannelAcceleration?: boolean;
  /** 是否启用物理WebQueue加速 */
  enablePhysicsWebQueueAcceleration?: boolean;
  /** 是否启用物理WebStack加速 */
  enablePhysicsWebStackAcceleration?: boolean;
  /** 是否启用物理WebHeap加速 */
  enablePhysicsWebHeapAcceleration?: boolean;
  /** 是否启用物理WebList加速 */
  enablePhysicsWebListAcceleration?: boolean;
  /** 是否启用物理WebArray加速 */
  enablePhysicsWebArrayAcceleration?: boolean;
  /** 是否启用物理WebVector加速 */
  enablePhysicsWebVectorAcceleration?: boolean;
  /** 是否启用物理WebMatrix加速 */
  enablePhysicsWebMatrixAcceleration?: boolean;
  /** 是否启用物理WebTensor加速 */
  enablePhysicsWebTensorAcceleration?: boolean;
  /** 是否启用物理WebGraph加速 */
  enablePhysicsWebGraphAcceleration?: boolean;
  /** 是否启用物理WebTree加速 */
  enablePhysicsWebTreeAcceleration?: boolean;
  /** 是否启用物理WebMap加速 */
  enablePhysicsWebMapAcceleration?: boolean;
  /** 是否启用物理WebSet加速 */
  enablePhysicsWebSetAcceleration?: boolean;
  /** 是否启用物理WebDictionary加速 */
  enablePhysicsWebDictionaryAcceleration?: boolean;
  /** 是否启用物理WebTable加速 */
  enablePhysicsWebTableAcceleration?: boolean;
  /** 是否启用物理WebDatabase加速 */
  enablePhysicsWebDatabaseAcceleration?: boolean;
  /** 是否启用物理WebDisk加速 */
  enablePhysicsWebDiskAcceleration?: boolean;
  /** 是否启用物理WebFile加速 */
  enablePhysicsWebFileAcceleration?: boolean;
  /** 是否启用物理WebDirectory加速 */
  enablePhysicsWebDirectoryAcceleration?: boolean;
  /** 是否启用物理WebPath加速 */
  enablePhysicsWebPathAcceleration?: boolean;
  /** 是否启用物理WebURL加速 */
  enablePhysicsWebURLAcceleration?: boolean;
  /** 是否启用物理WebURI加速 */
  enablePhysicsWebURIAcceleration?: boolean;
  /** 是否启用物理WebHTTP加速 */
  enablePhysicsWebHTTPAcceleration?: boolean;
  /** 是否启用物理WebHTTPS加速 */
  enablePhysicsWebHTTPSAcceleration?: boolean;
  /** 是否启用物理WebFTP加速 */
  enablePhysicsWebFTPAcceleration?: boolean;
  /** 是否启用物理WebSFTP加速 */
  enablePhysicsWebSFTPAcceleration?: boolean;
  /** 是否启用物理WebSSH加速 */
  enablePhysicsWebSSHAcceleration?: boolean;
  /** 是否启用物理WebTelnet加速 */
  enablePhysicsWebTelnetAcceleration?: boolean;
  /** 是否启用物理WebRDP加速 */
  enablePhysicsWebRDPAcceleration?: boolean;
  /** 是否启用物理WebVNC加速 */
  enablePhysicsWebVNCAcceleration?: boolean;
  /** 是否启用物理WebSMB加速 */
  enablePhysicsWebSMBAcceleration?: boolean;
  /** 是否启用物理WebNFS加速 */
  enablePhysicsWebNFSAcceleration?: boolean;
  /** 是否启用物理WebCIFS加速 */
  enablePhysicsWebCIFSAcceleration?: boolean;
  /** 是否启用物理WebWebDAV加速 */
  enablePhysicsWebWebDAVAcceleration?: boolean;
  /** 是否启用物理WebCalDAV加速 */
  enablePhysicsWebCalDAVAcceleration?: boolean;
  /** 是否启用物理WebCardDAV加速 */
  enablePhysicsWebCardDAVAcceleration?: boolean;
  /** 是否启用物理WebIMAPAcceleration */
  enablePhysicsWebIMAPAcceleration?: boolean;
  /** 是否启用物理WebPOP3Acceleration */
  enablePhysicsWebPOP3Acceleration?: boolean;
  /** 是否启用物理WebSMTPAcceleration */
  enablePhysicsWebSMTPAcceleration?: boolean;
  /** 是否启用物理WebDNSAcceleration */
  enablePhysicsWebDNSAcceleration?: boolean;
  /** 是否启用物理WebDHCPAcceleration */
  enablePhysicsWebDHCPAcceleration?: boolean;
  /** 是否启用物理WebNTPAcceleration */
  enablePhysicsWebNTPAcceleration?: boolean;
  /** 是否启用物理WebSNMPAcceleration */
  enablePhysicsWebSNMPAcceleration?: boolean;
  /** 是否启用物理WebSNTPAcceleration */
  enablePhysicsWebSNTPAcceleration?: boolean;
  /** 是否启用物理WebSyslogAcceleration */
  enablePhysicsWebSyslogAcceleration?: boolean;
  /** 是否启用物理WebTFTPAcceleration */
  enablePhysicsWebTFTPAcceleration?: boolean;
  /** 是否启用物理WebUDPAcceleration */
  enablePhysicsWebUDPAcceleration?: boolean;
  /** 是否启用物理WebTCPAcceleration */
  enablePhysicsWebTCPAcceleration?: boolean;
  /** 是否启用物理WebICMPAcceleration */
  enablePhysicsWebICMPAcceleration?: boolean;
  /** 是否启用物理WebIPAcceleration */
  enablePhysicsWebIPAcceleration?: boolean;
  /** 是否启用物理WebIPv4Acceleration */
  enablePhysicsWebIPv4Acceleration?: boolean;
  /** 是否启用物理WebIPv6Acceleration */
  enablePhysicsWebIPv6Acceleration?: boolean;
  /** 是否启用物理WebARPAcceleration */
  enablePhysicsWebARPAcceleration?: boolean;
  /** 是否启用物理WebRARPAcceleration */
  enablePhysicsWebRARPAcceleration?: boolean;
  /** 是否启用物理WebMACAcceleration */
  enablePhysicsWebMACAcceleration?: boolean;
  /** 是否启用物理WebEthernetAcceleration */
  enablePhysicsWebEthernetAcceleration?: boolean;
  /** 是否启用物理WebWiFiAcceleration */
  enablePhysicsWebWiFiAcceleration?: boolean;
  /** 是否启用物理WebBluetoothAcceleration */
  enablePhysicsWebBluetoothAcceleration?: boolean;
  /** 是否启用物理WebZigBeeAcceleration */
  enablePhysicsWebZigBeeAcceleration?: boolean;
  /** 是否启用物理WebZ-WaveAcceleration */
  enablePhysicsWebZ_WaveAcceleration?: boolean;
  /** 是否启用物理WebLoRaAcceleration */
  enablePhysicsWebLoRaAcceleration?: boolean;
  /** 是否启用物理WebSigFoxAcceleration */
  enablePhysicsWebSigFoxAcceleration?: boolean;
  /** 是否启用物理WebNB-IoTAcceleration */
  enablePhysicsWebNB_IoTAcceleration?: boolean;
  /** 是否启用物理WebLTE-MAcceleration */
  enablePhysicsWebLTE_MAcceleration?: boolean;
  /** 是否启用物理Web5GAcceleration */
  enablePhysicsWeb5GAcceleration?: boolean;
  /** 是否启用物理Web4GAcceleration */
  enablePhysicsWeb4GAcceleration?: boolean;
  /** 是否启用物理Web3GAcceleration */
  enablePhysicsWeb3GAcceleration?: boolean;
  /** 是否启用物理Web2GAcceleration */
  enablePhysicsWeb2GAcceleration?: boolean;
  /** 是否启用物理Web1GAcceleration */
  enablePhysicsWeb1GAcceleration?: boolean;
  /** 是否启用物理WebGSMAcceleration */
  enablePhysicsWebGSMAcceleration?: boolean;
  /** 是否启用物理WebCDMAAcceleration */
  enablePhysicsWebCDMAAcceleration?: boolean;
  /** 是否启用物理WebTDMAAcceleration */
  enablePhysicsWebTDMAAcceleration?: boolean;
  /** 是否启用物理WebFDMAAcceleration */
  enablePhysicsWebFDMAAcceleration?: boolean;
  /** 是否启用物理WebOFDMAAcceleration */
  enablePhysicsWebOFDMAAcceleration?: boolean;
  /** 是否启用物理WebSCFDMAAcceleration */
  enablePhysicsWebSCFDMAAcceleration?: boolean;
  /** 是否启用物理WebCDMA2000Acceleration */
  enablePhysicsWebCDMA2000Acceleration?: boolean;
  /** 是否启用物理WebWCDMAAcceleration */
  enablePhysicsWebWCDMAAcceleration?: boolean;
  /** 是否启用物理WebHSDPAAcceleration */
  enablePhysicsWebHSDPAAcceleration?: boolean;
  /** 是否启用物理WebHSUPAAcceleration */
  enablePhysicsWebHSUPAAcceleration?: boolean;
  /** 是否启用物理WebHSPAAcceleration */
  enablePhysicsWebHSPAAcceleration?: boolean;
  /** 是否启用物理WebHSPA+Acceleration */
  enablePhysicsWebHSPAplusAcceleration?: boolean;
  /** 是否启用物理WebDC-HSDPAAcceleration */
  enablePhysicsWebDC_HSDPAAcceleration?: boolean;
  /** 是否启用物理WebDC-HSPAAcceleration */
  enablePhysicsWebDC_HSPAAcceleration?: boolean;
  /** 是否启用物理WebDC-HSPA+Acceleration */
  enablePhysicsWebDC_HSPAplusAcceleration?: boolean;
  /** 是否启用物理WebLTEAcceleration */
  enablePhysicsWebLTEAcceleration?: boolean;
  /** 是否启用物理WebLTE-AAcceleration */
  enablePhysicsWebLTE_AAcceleration?: boolean;
  /** 是否启用物理WebLTE-BAcceleration */
  enablePhysicsWebLTE_BAcceleration?: boolean;
  /** 是否启用物理WebLTE-UAcceleration */
  enablePhysicsWebLTE_UAcceleration?: boolean;
  /** 是否启用物理WebLTE-LAA */
  enablePhysicsWebLTE_LAA?: boolean;
}

/**
 * 水体物理优化系统
 * 用于优化水体物理模拟性能，特别是在移动设备上
 */
export class WaterPhysicsOptimizationSystem extends System {
  /** 配置 */
  private config: WaterPhysicsOptimizationSystemConfig;
  /** 水体组件映射 */
  private waterBodies: Map<string, WaterBodyComponent> = new Map();
  /** 水体物理系统 */
  private waterPhysicsSystem: WaterPhysicsSystem | null = null;
  /** 设备能力 */
  private deviceCapabilities: DeviceCapabilities;
  /** 性能监控 */
  private performanceMonitor: PerformanceMonitor;
  /** 当前性能级别 */
  private currentPerformanceLevel: DevicePerformanceLevel;
  /** 帧计数器 */
  private frameCount: number = 0;
  /** 监控数据 */
  private monitorData: {
    fps: number;
    cpuUsage: number;
    gpuUsage: number;
    memoryUsage: number;
    batteryLevel: number;
    temperature: number;
    waterBodyCount: number;
    waterParticleCount: number;
    waterCollisionCount: number;
    waterUpdateTime: number;
    waterPhysicsTime: number;
    waterRenderTime: number;
  } = {
    fps: 60,
    cpuUsage: 0,
    gpuUsage: 0,
    memoryUsage: 0,
    batteryLevel: 100,
    temperature: 25,
    waterBodyCount: 0,
    waterParticleCount: 0,
    waterCollisionCount: 0,
    waterUpdateTime: 0,
    waterPhysicsTime: 0,
    waterRenderTime: 0
  };
  /** 性能配置 */
  private performanceConfigs: Record<DevicePerformanceLevel, any> = {
    [DevicePerformanceLevel.LOW]: {
      waterResolution: 32,
      waterChunkSize: 32,
      waterLODLevels: 2,
      waterUpdateFrequency: 3,
      waterPhysicsUpdateFrequency: 4,
      enableMultithreading: false,
      workerCount: 1,
      enableSpatialPartitioning: true,
      spatialGridSize: 32,
      enableAdaptiveUpdate: true,
      minUpdateFrequency: 4,
      maxUpdateFrequency: 8,
      enablePhysicsLOD: true,
      enablePhysicsSleeping: true,
      enablePhysicsCaching: true,
      enablePhysicsPrediction: false,
      enablePhysicsInterpolation: true,
      enablePhysicsSimplification: true,
      enablePhysicsInstancing: false,
      enablePhysicsBatching: true,
      enablePhysicsCulling: true,
      enablePhysicsPriority: true,
      enablePhysicsDeferredUpdate: true,
      enablePhysicsAsyncUpdate: false,
      maxWaterParticles: 1000,
      maxWaterCollisions: 500,
      maxWaterBodies: 5,
      maxWaterChunks: 16,
      maxWaterLODLevel: 2,
      maxWaterUpdateTime: 5,
      maxWaterPhysicsTime: 8,
      maxWaterRenderTime: 5,
      waterSimulationQuality: 'low',
      waterCollisionQuality: 'low',
      waterInteractionQuality: 'low',
      waterWaveQuality: 'low',
      waterFlowQuality: 'low',
      waterBuoyancyQuality: 'low',
      waterSplashQuality: 'low',
      waterRippleQuality: 'low',
      waterFoamQuality: 'low',
      waterBubbleQuality: 'low',
      waterMistQuality: 'low',
      waterDropletQuality: 'low',
      waterSurfaceTensionQuality: 'low',
      waterViscosityQuality: 'low',
      waterDensityQuality: 'low',
      waterPressureQuality: 'low',
      waterTemperatureQuality: 'low',
      waterSalinityQuality: 'low',
      waterTurbidityQuality: 'low',
      waterColorQuality: 'low',
      waterTransparencyQuality: 'low',
      waterReflectionQuality: 'low',
      waterRefractionQuality: 'low',
      waterCausticsQuality: 'low',
      waterVolumetricQuality: 'low',
      waterShadowQuality: 'low',
      waterAmbientOcclusionQuality: 'low',
      waterSubsurfaceScatteringQuality: 'low',
      waterGlobalIlluminationQuality: 'low',
      waterScreenSpaceReflectionQuality: 'low',
      waterScreenSpaceRefractionQuality: 'low',
      waterScreenSpaceCausticsQuality: 'low',
      waterScreenSpaceVolumetricQuality: 'low',
      waterScreenSpaceShadowQuality: 'low',
      waterScreenSpaceAmbientOcclusionQuality: 'low',
      waterScreenSpaceSubsurfaceScatteringQuality: 'low',
      waterScreenSpaceGlobalIlluminationQuality: 'low'
    },
    [DevicePerformanceLevel.MEDIUM]: {
      waterResolution: 64,
      waterChunkSize: 64,
      waterLODLevels: 3,
      waterUpdateFrequency: 2,
      waterPhysicsUpdateFrequency: 2,
      enableMultithreading: true,
      workerCount: 2,
      enableSpatialPartitioning: true,
      spatialGridSize: 64,
      enableAdaptiveUpdate: true,
      minUpdateFrequency: 2,
      maxUpdateFrequency: 4,
      enablePhysicsLOD: true,
      enablePhysicsSleeping: true,
      enablePhysicsCaching: true,
      enablePhysicsPrediction: true,
      enablePhysicsInterpolation: true,
      enablePhysicsSimplification: true,
      enablePhysicsInstancing: true,
      enablePhysicsBatching: true,
      enablePhysicsCulling: true,
      enablePhysicsPriority: true,
      enablePhysicsDeferredUpdate: true,
      enablePhysicsAsyncUpdate: true,
      maxWaterParticles: 5000,
      maxWaterCollisions: 2000,
      maxWaterBodies: 10,
      maxWaterChunks: 32,
      maxWaterLODLevel: 3,
      maxWaterUpdateTime: 8,
      maxWaterPhysicsTime: 10,
      maxWaterRenderTime: 8,
      waterSimulationQuality: 'medium',
      waterCollisionQuality: 'medium',
      waterInteractionQuality: 'medium',
      waterWaveQuality: 'medium',
      waterFlowQuality: 'medium',
      waterBuoyancyQuality: 'medium',
      waterSplashQuality: 'medium',
      waterRippleQuality: 'medium',
      waterFoamQuality: 'medium',
      waterBubbleQuality: 'medium',
      waterMistQuality: 'medium',
      waterDropletQuality: 'medium',
      waterSurfaceTensionQuality: 'medium',
      waterViscosityQuality: 'medium',
      waterDensityQuality: 'medium',
      waterPressureQuality: 'medium',
      waterTemperatureQuality: 'medium',
      waterSalinityQuality: 'medium',
      waterTurbidityQuality: 'medium',
      waterColorQuality: 'medium',
      waterTransparencyQuality: 'medium',
      waterReflectionQuality: 'medium',
      waterRefractionQuality: 'medium',
      waterCausticsQuality: 'medium',
      waterVolumetricQuality: 'medium',
      waterShadowQuality: 'medium',
      waterAmbientOcclusionQuality: 'medium',
      waterSubsurfaceScatteringQuality: 'medium',
      waterGlobalIlluminationQuality: 'medium',
      waterScreenSpaceReflectionQuality: 'medium',
      waterScreenSpaceRefractionQuality: 'medium',
      waterScreenSpaceCausticsQuality: 'medium',
      waterScreenSpaceVolumetricQuality: 'medium',
      waterScreenSpaceShadowQuality: 'medium',
      waterScreenSpaceAmbientOcclusionQuality: 'medium',
      waterScreenSpaceSubsurfaceScatteringQuality: 'medium',
      waterScreenSpaceGlobalIlluminationQuality: 'medium'
    },
    [DevicePerformanceLevel.HIGH]: {
      waterResolution: 128,
      waterChunkSize: 128,
      waterLODLevels: 4,
      waterUpdateFrequency: 1,
      waterPhysicsUpdateFrequency: 1,
      enableMultithreading: true,
      workerCount: 4,
      enableSpatialPartitioning: true,
      spatialGridSize: 128,
      enableAdaptiveUpdate: true,
      minUpdateFrequency: 1,
      maxUpdateFrequency: 2,
      enablePhysicsLOD: true,
      enablePhysicsSleeping: true,
      enablePhysicsCaching: true,
      enablePhysicsPrediction: true,
      enablePhysicsInterpolation: true,
      enablePhysicsSimplification: true,
      enablePhysicsInstancing: true,
      enablePhysicsBatching: true,
      enablePhysicsCulling: true,
      enablePhysicsPriority: true,
      enablePhysicsDeferredUpdate: true,
      enablePhysicsAsyncUpdate: true,
      maxWaterParticles: 10000,
      maxWaterCollisions: 5000,
      maxWaterBodies: 20,
      maxWaterChunks: 64,
      maxWaterLODLevel: 4,
      maxWaterUpdateTime: 10,
      maxWaterPhysicsTime: 15,
      maxWaterRenderTime: 10,
      waterSimulationQuality: 'high',
      waterCollisionQuality: 'high',
      waterInteractionQuality: 'high',
      waterWaveQuality: 'high',
      waterFlowQuality: 'high',
      waterBuoyancyQuality: 'high',
      waterSplashQuality: 'high',
      waterRippleQuality: 'high',
      waterFoamQuality: 'high',
      waterBubbleQuality: 'high',
      waterMistQuality: 'high',
      waterDropletQuality: 'high',
      waterSurfaceTensionQuality: 'high',
      waterViscosityQuality: 'high',
      waterDensityQuality: 'high',
      waterPressureQuality: 'high',
      waterTemperatureQuality: 'high',
      waterSalinityQuality: 'high',
      waterTurbidityQuality: 'high',
      waterColorQuality: 'high',
      waterTransparencyQuality: 'high',
      waterReflectionQuality: 'high',
      waterRefractionQuality: 'high',
      waterCausticsQuality: 'high',
      waterVolumetricQuality: 'high',
      waterShadowQuality: 'high',
      waterAmbientOcclusionQuality: 'high',
      waterSubsurfaceScatteringQuality: 'high',
      waterGlobalIlluminationQuality: 'high',
      waterScreenSpaceReflectionQuality: 'high',
      waterScreenSpaceRefractionQuality: 'high',
      waterScreenSpaceCausticsQuality: 'high',
      waterScreenSpaceVolumetricQuality: 'high',
      waterScreenSpaceShadowQuality: 'high',
      waterScreenSpaceAmbientOcclusionQuality: 'high',
      waterScreenSpaceSubsurfaceScatteringQuality: 'high',
      waterScreenSpaceGlobalIlluminationQuality: 'high'
    }
  };

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: WaterPhysicsOptimizationSystemConfig = {}) {
    super(100); // 设置系统优先级

    // 设置默认配置
    this.config = {
      enabled: config.enabled !== undefined ? config.enabled : true,
      autoUpdate: config.autoUpdate !== undefined ? config.autoUpdate : true,
      updateFrequency: config.updateFrequency || 60,
      enableDebugVisualization: config.enableDebugVisualization || false,
      enablePerformanceMonitoring: config.enablePerformanceMonitoring || true,
      enableAutoOptimization: config.enableAutoOptimization !== undefined ? config.enableAutoOptimization : true,
      enableBatteryOptimization: config.enableBatteryOptimization !== undefined ? config.enableBatteryOptimization : true,
      enableTemperatureOptimization: config.enableTemperatureOptimization !== undefined ? config.enableTemperatureOptimization : true,
      targetFPS: config.targetFPS || 60,
      minAcceptableFPS: config.minAcceptableFPS || 30,
      defaultPerformanceLevel: config.defaultPerformanceLevel || DevicePerformanceLevel.MEDIUM,
      lowBatteryThreshold: config.lowBatteryThreshold || 20,
      highTemperatureThreshold: config.highTemperatureThreshold || 40,
      enableSpatialPartitioning: config.enableSpatialPartitioning !== undefined ? config.enableSpatialPartitioning : true,
      enableMultithreading: config.enableMultithreading !== undefined ? config.enableMultithreading : true,
      enableAdaptivePhysicsUpdate: config.enableAdaptivePhysicsUpdate !== undefined ? config.enableAdaptivePhysicsUpdate : true,
      enablePhysicsLOD: config.enablePhysicsLOD !== undefined ? config.enablePhysicsLOD : true,
      enablePhysicsSleeping: config.enablePhysicsSleeping !== undefined ? config.enablePhysicsSleeping : true,
      enablePhysicsCaching: config.enablePhysicsCaching !== undefined ? config.enablePhysicsCaching : true,
      enablePhysicsPrediction: config.enablePhysicsPrediction !== undefined ? config.enablePhysicsPrediction : true,
      enablePhysicsInterpolation: config.enablePhysicsInterpolation !== undefined ? config.enablePhysicsInterpolation : true,
      enablePhysicsSimplification: config.enablePhysicsSimplification !== undefined ? config.enablePhysicsSimplification : true,
      enablePhysicsInstancing: config.enablePhysicsInstancing !== undefined ? config.enablePhysicsInstancing : true,
      enablePhysicsBatching: config.enablePhysicsBatching !== undefined ? config.enablePhysicsBatching : true,
      enablePhysicsCulling: config.enablePhysicsCulling !== undefined ? config.enablePhysicsCulling : true,
      enablePhysicsPriority: config.enablePhysicsPriority !== undefined ? config.enablePhysicsPriority : true,
      enablePhysicsDeferredUpdate: config.enablePhysicsDeferredUpdate !== undefined ? config.enablePhysicsDeferredUpdate : true,
      enablePhysicsAsyncUpdate: config.enablePhysicsAsyncUpdate !== undefined ? config.enablePhysicsAsyncUpdate : true,
      enablePhysicsGPUAcceleration: config.enablePhysicsGPUAcceleration !== undefined ? config.enablePhysicsGPUAcceleration : false,
      enablePhysicsSIMDAcceleration: config.enablePhysicsSIMDAcceleration !== undefined ? config.enablePhysicsSIMDAcceleration : false,
      enablePhysicsWebAssemblyAcceleration: config.enablePhysicsWebAssemblyAcceleration !== undefined ? config.enablePhysicsWebAssemblyAcceleration : false,
      enablePhysicsSharedArrayBufferAcceleration: config.enablePhysicsSharedArrayBufferAcceleration !== undefined ? config.enablePhysicsSharedArrayBufferAcceleration : false,
      enablePhysicsWebWorkerAcceleration: config.enablePhysicsWebWorkerAcceleration !== undefined ? config.enablePhysicsWebWorkerAcceleration : true
    };

    // 获取设备能力
    this.deviceCapabilities = DeviceCapabilities.getInstance();

    // 获取性能监控
    this.performanceMonitor = PerformanceMonitor.getInstance();

    // 设置当前性能级别
    this.currentPerformanceLevel = this.config.defaultPerformanceLevel!;

    // 初始化系统
    this.initialize();

    Debug.log('WaterPhysicsOptimizationSystem', '水体物理优化系统已创建');
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    // 获取水体物理系统
    this.waterPhysicsSystem = this.world?.getSystem(WaterPhysicsSystem) || null;

    // 如果没有找到水体物理系统，则创建一个
    if (!this.waterPhysicsSystem) {
      Debug.warn('WaterPhysicsOptimizationSystem', '未找到水体物理系统，将创建一个新的水体物理系统');
      this.waterPhysicsSystem = new WaterPhysicsSystem(this.world);
      this.world.addSystem(this.waterPhysicsSystem);
    }

    // 应用初始性能配置
    this.applyPerformanceConfig(this.currentPerformanceLevel);
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(_deltaTime: number): void {
    if (!this.config.enabled || !this.config.autoUpdate) {
      return;
    }

    // 按照更新频率更新
    this.frameCount++;
    if (this.frameCount % this.config.updateFrequency! !== 0) {
      return;
    }

    // 如果启用了性能监控，开始计时
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.beginMeasure('waterPhysicsOptimizationUpdate');
    }

    // 更新监控数据
    this.updateMonitorData();

    // 如果启用了自动优化，根据监控数据调整性能
    if (this.config.enableAutoOptimization) {
      this.adjustPerformance();
    }

    // 如果启用了电池优化，根据电池电量调整性能
    if (this.config.enableBatteryOptimization) {
      this.adjustPerformanceByBattery();
    }

    // 如果启用了温度优化，根据设备温度调整性能
    if (this.config.enableTemperatureOptimization) {
      this.adjustPerformanceByTemperature();
    }

    // 如果启用了性能监控，结束计时
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMonitor.endMeasure('waterPhysicsOptimizationUpdate');
    }
  }

  /**
   * 更新监控数据
   */
  private updateMonitorData(): void {
    // 更新FPS - 从性能监控器获取或使用设备能力
    const report = this.performanceMonitor.getReport();
    this.monitorData.fps = report.metrics.fps?.value || this.deviceCapabilities.getCurrentFPS();

    // 更新CPU使用率
    this.monitorData.cpuUsage = report.metrics.cpuUsage?.value || this.deviceCapabilities.getCPUUsage();

    // 更新GPU使用率
    this.monitorData.gpuUsage = report.metrics.gpuUsage?.value || this.deviceCapabilities.getGPUUsage();

    // 更新内存使用率
    this.monitorData.memoryUsage = report.metrics.memoryUsage?.value || this.deviceCapabilities.getMemoryUsage();

    // 更新电池电量
    this.monitorData.batteryLevel = this.deviceCapabilities.getBatteryLevel();

    // 更新设备温度
    this.monitorData.temperature = this.deviceCapabilities.getTemperature();

    // 更新水体数量
    this.monitorData.waterBodyCount = this.waterBodies.size;

    // 更新水体粒子数量
    this.monitorData.waterParticleCount = this.calculateWaterParticleCount();

    // 更新水体碰撞数量
    this.monitorData.waterCollisionCount = this.calculateWaterCollisionCount();

    // 更新水体更新时间 - 使用默认值，因为 PerformanceMonitor 没有 getAverageTime 方法
    this.monitorData.waterUpdateTime = report.metrics.waterUpdateTime?.value || 0;

    // 更新水体物理时间
    this.monitorData.waterPhysicsTime = report.metrics.waterPhysicsTime?.value || 0;

    // 更新水体渲染时间
    this.monitorData.waterRenderTime = report.metrics.waterRenderTime?.value || 0;
  }

  /**
   * 计算水体粒子数量
   * @returns 水体粒子数量
   */
  private calculateWaterParticleCount(): number {
    let count = 0;
    for (const [,] of this.waterBodies) {
      // 由于 WaterBodyComponent 没有 getParticleCount 方法，使用估算值
      count += 1000; // 每个水体估算1000个粒子
    }
    return count;
  }

  /**
   * 计算水体碰撞数量
   * @returns 水体碰撞数量
   */
  private calculateWaterCollisionCount(): number {
    let count = 0;
    for (const [,] of this.waterBodies) {
      // 由于 WaterBodyComponent 没有 getCollisionCount 方法，使用估算值
      count += 50; // 每个水体估算50个碰撞
    }
    return count;
  }

  /**
   * 根据性能调整性能级别
   */
  private adjustPerformance(): void {
    // 如果帧率低于最小可接受帧率，降低性能级别
    if (this.monitorData.fps < this.config.minAcceptableFPS!) {
      this.decreasePerformanceLevel();
    }
    // 如果帧率高于目标帧率的1.2倍，提高性能级别
    else if (this.monitorData.fps > this.config.targetFPS! * 1.2) {
      this.increasePerformanceLevel();
    }
  }

  /**
   * 根据电池电量调整性能级别
   */
  private adjustPerformanceByBattery(): void {
    // 如果电池电量低于阈值，降低性能级别
    if (this.monitorData.batteryLevel < this.config.lowBatteryThreshold!) {
      // 如果当前性能级别高于低性能级别，降低到低性能级别
      if (this.currentPerformanceLevel > DevicePerformanceLevel.LOW) {
        this.setPerformanceLevel(DevicePerformanceLevel.LOW);
      }
    }
  }

  /**
   * 根据设备温度调整性能级别
   */
  private adjustPerformanceByTemperature(): void {
    // 如果设备温度高于阈值，降低性能级别
    if (this.monitorData.temperature > this.config.highTemperatureThreshold!) {
      this.decreasePerformanceLevel();
    }
  }

  /**
   * 提高性能级别
   */
  private increasePerformanceLevel(): void {
    // 如果当前性能级别已经是最高级别，则不再提高
    if (this.currentPerformanceLevel >= DevicePerformanceLevel.HIGH) {
      return;
    }

    // 提高性能级别
    const newLevel = this.currentPerformanceLevel + 1;
    this.setPerformanceLevel(newLevel);
  }

  /**
   * 降低性能级别
   */
  private decreasePerformanceLevel(): void {
    // 如果当前性能级别已经是最低级别，则不再降低
    if (this.currentPerformanceLevel <= DevicePerformanceLevel.LOW) {
      return;
    }

    // 降低性能级别
    const newLevel = this.currentPerformanceLevel - 1;
    this.setPerformanceLevel(newLevel);
  }

  /**
   * 设置性能级别
   * @param level 性能级别
   */
  public setPerformanceLevel(level: DevicePerformanceLevel): void {
    // 如果性能级别没有变化，则不需要更新
    if (level === this.currentPerformanceLevel) {
      return;
    }

    // 更新当前性能级别
    this.currentPerformanceLevel = level;

    // 应用性能配置
    this.applyPerformanceConfig(level);

    Debug.log('WaterPhysicsOptimizationSystem', `设置性能级别: ${DevicePerformanceLevel[level]}`);
  }

  /**
   * 应用性能配置
   * @param level 性能级别
   */
  private applyPerformanceConfig(level: DevicePerformanceLevel): void {
    // 获取性能配置
    const config = this.performanceConfigs[level];

    // 应用水体物理系统配置
    if (this.waterPhysicsSystem) {
      // 注意：WaterPhysicsSystem 没有 setConfig 方法，这里仅作为示例
      // 在实际实现中，需要根据 WaterPhysicsSystem 的实际接口来配置
      Debug.log('WaterPhysicsOptimizationSystem', `应用性能级别 ${DevicePerformanceLevel[level]} 的配置，更新频率: ${config.waterPhysicsUpdateFrequency}`);

      // 可以在这里调用 WaterPhysicsSystem 的实际方法来配置性能参数
      // 例如：this.waterPhysicsSystem.setUpdateFrequency(config.waterPhysicsUpdateFrequency);
    }

    // 更新所有水体组件
    for (const [, waterBody] of this.waterBodies) {
      this.updateWaterBodyForMobile(waterBody, level);
    }
  }

  /**
   * 更新水体组件以适应移动设备
   * @param waterBody 水体组件
   * @param level 性能级别
   */
  private updateWaterBodyForMobile(waterBody: WaterBodyComponent, level: DevicePerformanceLevel): void {
    // 获取性能配置
    const config = this.performanceConfigs[level];

    // 注意：以下方法在 WaterBodyComponent 中不存在，这里仅作为示例
    // 在实际实现中，需要根据 WaterBodyComponent 的实际接口来配置

    Debug.log('WaterPhysicsOptimizationSystem', `为水体组件应用性能级别 ${DevicePerformanceLevel[level]} 的配置，分辨率: ${config.waterResolution}`);

    // 可以在这里调用 WaterBodyComponent 的实际方法来配置性能参数
    // 例如：
    // - 调整水体网格分辨率
    // - 设置波动参数
    // - 配置流动参数
    // - 启用/禁用特效

    // 示例：使用现有的方法
    if (level <= DevicePerformanceLevel.LOW) {
      // 低性能设备：禁用水体组件
      waterBody.setEnabled(false);
    } else {
      // 中高性能设备：启用水体组件
      waterBody.setEnabled(true);
    }
  }

  /**
   * 添加水体组件
   * @param entity 实体
   * @param component 水体组件
   */
  public addWaterBody(entity: Entity, component: WaterBodyComponent): void {
    this.waterBodies.set(entity.id, component);

    // 更新水体组件以适应移动设备
    this.updateWaterBodyForMobile(component, this.currentPerformanceLevel);

    Debug.log('WaterPhysicsOptimizationSystem', `添加水体组件: ${entity.id}`);
  }

  /**
   * 移除水体组件
   * @param entity 实体
   */
  public removeWaterBody(entity: Entity): void {
    this.waterBodies.delete(entity.id);

    Debug.log('WaterPhysicsOptimizationSystem', `移除水体组件: ${entity.id}`);
  }

  /**
   * 获取当前性能级别
   * @returns 当前性能级别
   */
  public getCurrentPerformanceLevel(): DevicePerformanceLevel {
    return this.currentPerformanceLevel;
  }

  /**
   * 获取监控数据
   * @returns 监控数据
   */
  public getMonitorData(): any {
    return { ...this.monitorData };
  }

  /**
   * 获取性能配置
   * @param level 性能级别
   * @returns 性能配置
   */
  public getPerformanceConfig(level: DevicePerformanceLevel): any {
    return { ...this.performanceConfigs[level] };
  }

  /**
   * 设置性能配置
   * @param level 性能级别
   * @param config 性能配置
   */
  public setPerformanceConfig(level: DevicePerformanceLevel, config: any): void {
    this.performanceConfigs[level] = { ...this.performanceConfigs[level], ...config };

    // 如果当前性能级别是被修改的级别，则应用新配置
    if (this.currentPerformanceLevel === level) {
      this.applyPerformanceConfig(level);
    }
  }

  /**
   * 配置系统
   * @param config 配置
   */
  public configure(config: Partial<WaterPhysicsOptimizationSystemConfig>): void {
    // 更新配置
    this.config = { ...this.config, ...config };

    // 如果更新了默认性能级别，则应用新的性能级别
    if (config.defaultPerformanceLevel !== undefined && config.defaultPerformanceLevel !== this.currentPerformanceLevel) {
      this.setPerformanceLevel(config.defaultPerformanceLevel);
    }
  }

  /**
   * 启用系统
   */
  public enable(): void {
    this.config.enabled = true;
  }

  /**
   * 禁用系统
   */
  public disable(): void {
    this.config.enabled = false;
  }

  /**
   * 启用自动更新
   */
  public enableAutoUpdate(): void {
    this.config.autoUpdate = true;
  }

  /**
   * 禁用自动更新
   */
  public disableAutoUpdate(): void {
    this.config.autoUpdate = false;
  }

  /**
   * 启用自动优化
   */
  public enableAutoOptimization(): void {
    this.config.enableAutoOptimization = true;
  }

  /**
   * 禁用自动优化
   */
  public disableAutoOptimization(): void {
    this.config.enableAutoOptimization = false;
  }

  /**
   * 启用电池优化
   */
  public enableBatteryOptimization(): void {
    this.config.enableBatteryOptimization = true;
  }

  /**
   * 禁用电池优化
   */
  public disableBatteryOptimization(): void {
    this.config.enableBatteryOptimization = false;
  }

  /**
   * 启用温度优化
   */
  public enableTemperatureOptimization(): void {
    this.config.enableTemperatureOptimization = true;
  }

  /**
   * 禁用温度优化
   */
  public disableTemperatureOptimization(): void {
    this.config.enableTemperatureOptimization = false;
  }

  /**
   * 启用性能监控
   */
  public enablePerformanceMonitoring(): void {
    this.config.enablePerformanceMonitoring = true;
  }

  /**
   * 禁用性能监控
   */
  public disablePerformanceMonitoring(): void {
    this.config.enablePerformanceMonitoring = false;
  }

  /**
   * 启用调试可视化
   */
  public enableDebugVisualization(): void {
    this.config.enableDebugVisualization = true;
  }

  /**
   * 禁用调试可视化
   */
  public disableDebugVisualization(): void {
    this.config.enableDebugVisualization = false;
  }

  /**
   * 设置更新频率
   * @param frequency 更新频率
   */
  public setUpdateFrequency(frequency: number): void {
    this.config.updateFrequency = frequency;
  }

  /**
   * 设置目标帧率
   * @param fps 目标帧率
   */
  public setTargetFPS(fps: number): void {
    this.config.targetFPS = fps;
  }

  /**
   * 设置最小可接受帧率
   * @param fps 最小可接受帧率
   */
  public setMinAcceptableFPS(fps: number): void {
    this.config.minAcceptableFPS = fps;
  }

  /**
   * 设置低电量阈值
   * @param threshold 低电量阈值（百分比）
   */
  public setLowBatteryThreshold(threshold: number): void {
    this.config.lowBatteryThreshold = threshold;
  }

  /**
   * 设置高温阈值
   * @param threshold 高温阈值（摄氏度）
   */
  public setHighTemperatureThreshold(threshold: number): void {
    this.config.highTemperatureThreshold = threshold;
  }
}
